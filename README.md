# EnterpriseMqttBroker

## 项目概括
本项目旨在开发一个基于.NET 8的企业级高性能MQTT Broker应用程序，支持MQTT 3.1.1和5.0协议，提供WebSocket和TCP连接支持，具备完整的客户端管理、消息持久化、权限控制和实时监控功能。目标部署环境为Windows Server 2019 + IIS 10，重点关注高并发处理能力和企业级可靠性。

## 技术选型
### 核心框架
- **主框架**: ASP.NET Core 8.0 Web API
- **MQTT库**: MQTTnet 4.3.x（最新稳定版，支持MQTT 3.1.1/5.0）
- **运行时**: .NET 8 LTS
- **部署环境**: Windows Server 2019 + IIS 10

### 数据存储架构
- **主数据库**: SQL Server 2019/2022（用户认证、权限配置、消息持久化）
  - 理由：与Windows Server环境完美集成，支持高并发，企业级可靠性和备份恢复
- **缓存层**: Redis 7.x（会话状态、实时连接信息、消息队列缓存）
  - 理由：高性能内存存储，支持发布订阅模式，完美匹配MQTT场景
- **配置存储**: appsettings.json + SQL Server动态配置表

### 认证授权体系
- **多层认证机制**:
  - JWT Token认证（Web API访问）
  - MQTT客户端用户名/密码认证
  - 可选客户端证书认证（高安全场景）
- **权限控制**: 基于主题的RBAC（Role-Based Access Control）权限模型
- **会话管理**: Redis分布式会话存储

### 性能优化技术栈
- **异步I/O**: .NET 8异步编程模式（async/await）
- **并发处理**: Channel-based消息处理管道
- **连接管理**: 自定义高性能连接池
- **内存优化**: Span<T>、Memory<T>、ArrayPool<T>
- **日志系统**: Serilog + 结构化日志 + Seq/ELK集成

### 管理界面技术栈
- **后端API**: ASP.NET Core Web API + Swagger/OpenAPI
- **实时通信**: SignalR（实时监控数据推送）
- **前端框架**: React 18 + TypeScript + Ant Design Pro
- **状态管理**: Redux Toolkit + RTK Query
- **图表组件**: Apache ECharts（连接统计、消息流量图表）

### 开发和部署工具
- **版本控制**: Git
- **包管理**: NuGet + npm/yarn
- **构建工具**: MSBuild + Webpack
- **容器化**: Docker（可选，便于开发环境）
- **CI/CD**: GitHub Actions 或 Azure DevOps
- **监控**: Application Insights + 自定义性能计数器

## 项目结构 / 模块划分
```
EnterpriseMqttBroker/
├── src/
│   ├── EnterpriseMqttBroker.Core/           # 核心业务逻辑层
│   │   ├── Interfaces/                      # 接口定义
│   │   ├── Services/                        # 核心服务实现
│   │   ├── Models/                          # 领域模型
│   │   └── Exceptions/                      # 自定义异常
│   ├── EnterpriseMqttBroker.Infrastructure/ # 基础设施层
│   │   ├── Data/                           # 数据访问层
│   │   ├── Cache/                          # Redis缓存实现
│   │   ├── Logging/                        # 日志实现
│   │   └── Configuration/                  # 配置管理
│   ├── EnterpriseMqttBroker.MqttService/   # MQTT服务层
│   │   ├── Handlers/                       # MQTT事件处理器
│   │   ├── Managers/                       # 连接和会话管理
│   │   ├── Security/                       # 认证授权实现
│   │   └── Persistence/                    # 消息持久化
│   ├── EnterpriseMqttBroker.WebApi/        # Web API层
│   │   ├── Controllers/                    # API控制器
│   │   ├── Hubs/                          # SignalR Hub
│   │   ├── Middleware/                     # 中间件
│   │   └── DTOs/                          # 数据传输对象
│   └── EnterpriseMqttBroker.AdminUI/       # 管理界面前端
│       ├── src/components/                 # React组件
│       ├── src/pages/                      # 页面组件
│       ├── src/services/                   # API服务
│       └── src/utils/                      # 工具函数
├── tests/
│   ├── EnterpriseMqttBroker.UnitTests/     # 单元测试
│   ├── EnterpriseMqttBroker.IntegrationTests/ # 集成测试
│   └── EnterpriseMqttBroker.LoadTests/     # 性能测试
├── deployment/
│   ├── iis/                               # IIS部署配置
│   ├── scripts/                           # 部署脚本
│   └── docker/                            # Docker配置（可选）
├── docs/                                  # 项目文档
├── EnterpriseMqttBroker.sln              # 解决方案文件
└── README.md                             # 项目说明文档
```

## 核心功能 / 模块详解
### 1. MQTT服务核心引擎 (MqttService)
- **MqttServerManager**: MQTT服务器生命周期管理，支持TCP和WebSocket监听
- **ConnectionManager**: 客户端连接池管理，连接状态跟踪，异常连接清理
- **MessageRouter**: 高性能消息路由引擎，支持主题匹配和消息分发
- **ProtocolHandler**: MQTT 3.1.1/5.0协议处理，包括连接、发布、订阅、心跳等

### 2. 认证授权系统 (Security)
- **AuthenticationService**: 多层认证实现（JWT、用户名密码、证书）
- **AuthorizationService**: 基于主题的权限验证，支持通配符主题权限
- **UserManager**: 用户账户管理，密码策略，账户锁定机制
- **RoleManager**: 角色权限管理，支持动态权限分配

### 3. 消息持久化系统 (Persistence)
- **MessageStore**: 消息持久化存储，支持QoS 1/2消息可靠传递
- **RetainedMessageManager**: 保留消息管理，支持主题层级保留消息
- **SessionStore**: 客户端会话状态持久化，支持Clean Session和持久会话
- **WillMessageHandler**: 遗嘱消息处理，客户端异常断开时的消息发布

### 4. 实时监控系统 (Monitoring)
- **MetricsCollector**: 性能指标收集（连接数、消息吞吐量、延迟统计）
- **HealthCheckService**: 系统健康状态检查，包括数据库、Redis连接状态
- **AlertManager**: 告警管理，支持阈值告警和异常事件通知
- **LogAnalyzer**: 日志分析和异常检测

### 5. Web管理API (WebApi)
- **ClientController**: 客户端连接管理API（查看、断开、黑名单）
- **TopicController**: 主题管理API（权限配置、统计信息）
- **UserController**: 用户账户管理API（增删改查、权限分配）
- **MonitoringController**: 监控数据API（实时统计、历史数据查询）
- **ConfigurationController**: 系统配置管理API（动态配置更新）

## 数据模型设计
### 用户认证表 (Users)
```sql
Users: { 
  Id (GUID, PK), 
  Username (NVARCHAR(50) UNIQUE NOT NULL), 
  PasswordHash (NVARCHAR(255) NOT NULL), 
  Email (NVARCHAR(100)), 
  IsActive (BIT DEFAULT 1), 
  CreatedAt (DATETIME2 NOT NULL), 
  LastLoginAt (DATETIME2),
  FailedLoginAttempts (INT DEFAULT 0),
  LockoutEndTime (DATETIME2 NULL)
}
```

### 角色权限表 (Roles & Permissions)
```sql
Roles: { Id (GUID, PK), Name (NVARCHAR(50) UNIQUE), Description (NVARCHAR(200)) }
TopicPermissions: { 
  Id (GUID, PK), 
  RoleId (GUID, FK), 
  TopicPattern (NVARCHAR(255)), 
  CanPublish (BIT), 
  CanSubscribe (BIT) 
}
UserRoles: { UserId (GUID, FK), RoleId (GUID, FK) }
```

### 消息存储表 (Messages)
```sql
PersistedMessages: {
  Id (BIGINT IDENTITY, PK),
  ClientId (NVARCHAR(100) NOT NULL),
  Topic (NVARCHAR(255) NOT NULL),
  Payload (VARBINARY(MAX)),
  QoSLevel (TINYINT),
  Retain (BIT),
  CreatedAt (DATETIME2 NOT NULL),
  ExpiryInterval (INT NULL)
}
```

## 技术实现细节

### 数据访问层和实体模型 (已完成)
**实现时间**: 2025-01-15

#### 核心接口设计
- ✅ **通用仓储接口** (`IRepository<T>`): 提供标准CRUD操作、分页查询、条件筛选等通用数据访问方法
- ✅ **工作单元接口** (`IUnitOfWork`): 管理多个仓储的事务一致性，支持分布式事务操作
- ✅ **专用仓储接口**:
  - `IUserRepository`: 用户相关的高级查询（包含角色、权限查询、锁定状态管理）
  - `IMessageRepository`: 消息持久化的专用操作（主题模式匹配、保留消息、过期清理）

#### 实体模型配置
- ✅ **EF Core Fluent API配置**: 为所有实体配置了详细的数据库映射规则
  - **用户实体** (`User`): 包含认证信息、锁定机制、审计字段
  - **角色实体** (`Role`): 支持基于角色的权限控制
  - **权限实体** (`TopicPermission`): 支持MQTT主题级别的细粒度权限控制
  - **消息实体** (`PersistedMessage`): 支持QoS、保留消息、过期机制
  - **关联实体** (`UserRole`): 多对多关系映射

#### 数据库设计优化
- ✅ **索引策略**: 针对高频查询场景设计了20+个复合索引
  - 用户查询索引：用户名、邮箱、活跃状态、锁定状态
  - 消息查询索引：客户端ID、主题、时间范围、QoS级别、保留消息
  - 权限查询索引：角色-主题模式复合索引，发布/订阅权限过滤索引
- ✅ **表命名规范**: 统一使用 `Mqtt_` 前缀，避免与系统表冲突
- ✅ **数据类型优化**: 使用适当的数据类型（GUID主键、VARBINARY消息载荷、DATETIME2时间戳）

#### Repository模式实现
- ✅ **通用仓储实现** (`Repository<T>`):
  - 支持异步操作、表达式树查询、分页、批量操作
  - 实现了软删除、乐观并发控制
- ✅ **用户仓储实现** (`UserRepository`):
  - 包含角色的联合查询、用户名/邮箱唯一性检查
  - 批量登录时间更新、过期锁定清理
- ✅ **消息仓储实现** (`MessageRepository`):
  - MQTT通配符主题匹配、保留消息管理
  - 消息统计分析、客户端消息清理、过期消息自动清理

#### 数据库迁移
- ✅ **初始迁移文件**: 包含完整的表结构、索引、种子数据
- ✅ **种子数据**: 预置管理员账户、默认角色、基础权限配置
  - 管理员账户: `admin/admin123` (生产环境需修改)
  - 默认角色: Administrator(全权限)、User(基础MQTT权限)
  - 基础权限: 管理员全主题权限、普通用户限定主题权限

#### 依赖注入配置
- ✅ **服务注册扩展** (`ServiceCollectionExtensions`):
  - 数据库上下文配置（连接重试、超时设置、迁移程序集）
  - Redis缓存集成（支持内存缓存降级）
  - 健康检查配置（SQL Server、Redis连接状态监控）
- ✅ **生产环境优化**:
  - 连接池配置、查询优化、敏感数据日志控制
  - 自动数据库迁移、启动时健康检查

#### 技术特性
- ✅ **异步编程**: 全面使用 async/await 模式，支持取消令牌
- ✅ **事务管理**: 支持显式事务、分布式事务、事务回滚
- ✅ **性能优化**:
  - 使用 `ExecuteUpdateAsync`/`ExecuteDeleteAsync` 进行批量操作
  - 查询投影优化、Include策略优化
  - 分页查询性能优化
- ✅ **安全性**: BCrypt密码哈希、SQL注入防护、参数化查询

### 项目结构初始化 (已完成)
**实现时间**: 2024-01-15

#### 目录结构创建
- ✅ 完整的分层架构目录结构
- ✅ 源代码目录 (`src/`)：Core、Infrastructure、MqttService、WebApi、AdminUI
- ✅ 测试目录 (`tests/`)：UnitTests、IntegrationTests、LoadTests
- ✅ 部署目录 (`deployment/`)：IIS配置、脚本、Docker配置

#### 项目文件配置
- ✅ Visual Studio解决方案文件 (`EnterpriseMqttBroker.sln`)
- ✅ 各层级项目文件 (`.csproj`) 配置完成
- ✅ NuGet包依赖配置：
  - **Core层**: 基础抽象包 (DI、Logging、Configuration)
  - **Infrastructure层**: EF Core、Redis、Serilog日志
  - **MqttService层**: MQTTnet 4.3.x、异步处理
  - **WebApi层**: ASP.NET Core、SignalR、JWT认证、Swagger
  - **测试项目**: xUnit、Moq、FluentAssertions、Testcontainers

#### 基础配置文件
- ✅ 应用程序配置 (`appsettings.json`、`appsettings.Development.json`)
- ✅ IIS部署配置 (`web.config`) - 包含WebSocket支持、安全头、压缩等
- ✅ PowerShell自动化部署脚本 (`deploy-to-iis.ps1`)
- ✅ Git忽略文件 (`.gitignore`) - .NET项目标准配置

#### 核心数据模型
- ✅ 用户认证模型 (`User`、`Role`、`UserRole`)
- ✅ 权限控制模型 (`TopicPermission`)
- ✅ 消息持久化模型 (`PersistedMessage`)
- ✅ 核心服务接口定义 (`IUserService`、`IMqttServerService`)

#### 技术特性配置
- ✅ 多环境配置支持 (Development/Production)
- ✅ 结构化日志配置 (Serilog + Console + File + Seq)
- ✅ 安全配置 (JWT、密码策略、账户锁定)
- ✅ MQTT服务配置 (TCP/WebSocket端口、连接限制、QoS支持)
- ✅ 监控配置 (健康检查、性能指标收集)

#### 部署就绪特性
- ✅ IIS集成配置 (应用程序池、WebSocket、HTTPS重定向)
- ✅ 自动化部署脚本 (构建、部署、权限设置、健康检查)
- ✅ 生产环境优化 (压缩、缓存、安全头)

#### NuGet包版本更新 (2024-01-15)
- ✅ **核心框架包更新至最新版本**:
  - Microsoft.Extensions.* 系列: 9.0.6 (最新稳定版)
  - Entity Framework Core: 9.0.6 (支持.NET 8)
  - MQTTnet: 5.0.1.1416 (最新版本，性能优化)
  - Serilog: 4.3.0 (最新结构化日志)

- ✅ **Web API相关包更新**:
  - ASP.NET Core: 8.0.12 (LTS版本)
  - Swashbuckle.AspNetCore: 7.2.0 (OpenAPI 3.0支持)
  - JWT Bearer: 8.3.1 (安全漏洞修复)
  - API版本控制: Asp.Versioning.Mvc 8.1.0 (新版本)

- ✅ **测试框架包更新**:
  - xUnit: 2.9.2 (最新测试框架)
  - Moq: 4.20.72 (最新模拟框架)
  - FluentAssertions: 7.0.0 (增强断言)
  - Testcontainers: 4.1.0 (容器化测试)
  - NBomber: 6.0.1 (负载测试)

- ✅ **构建验证**: 整个解决方案成功构建，所有项目兼容.NET 8

## IIS部署特殊配置需求
### Web.config配置要点
- 启用WebSocket支持：`<webSocket enabled="true" />`
- 配置长连接超时：`<httpRuntime executionTimeout="3600" maxRequestLength="51200" />`
- 启用异步处理：`<system.webServer><handlers><add name="aspNetCore" ... /></handlers></system.webServer>`

### 应用程序池配置
- .NET CLR版本：无托管代码
- 托管管道模式：集成
- 启用32位应用程序：False
- 空闲超时：0（禁用自动回收）
- 最大工作进程：1（或根据服务器配置调整）

## 开发状态跟踪
| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| 项目结构初始化               | 已完成 | AI     | 2024-01-15   | 2024-01-15   | [技术实现细节](#项目结构初始化-已完成) |
| 数据访问层和实体模型         | 已完成 | AI     | 2024-01-18   | 2024-01-15   | [技术实现细节](#数据访问层和实体模型-已完成) |
| MQTT服务核心引擎             | 未开始 | AI     | 2024-01-20   |              |            |
| 认证授权系统                 | 未开始 | AI     | 2024-01-22   |              |            |
| 消息持久化系统               | 未开始 | AI     | 2024-01-25   |              |            |
| Web管理API                   | 未开始 | AI     | 2024-01-28   |              |            |
| 实时监控系统                 | 未开始 | AI     | 2024-01-30   |              |            |
| 管理界面前端                 | 未开始 | AI     | 2024-02-05   |              |            |
| IIS部署配置                  | 未开始 | AI     | 2024-02-08   |              |            |
| 性能测试和优化               | 未开始 | AI     | 2024-02-10   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案]

## 环境设置与运行指南
### 开发环境要求
- Visual Studio 2022 17.8+ 或 VS Code + C# Dev Kit
- .NET 8 SDK
- SQL Server 2019+ 或 SQL Server Express
- Redis 7.x
- Node.js 18+ (用于前端开发)

### 运行步骤
1. 克隆项目：`git clone <repository-url>`
2. 还原NuGet包：`dotnet restore`
3. 配置数据库连接字符串（appsettings.json）
4. 运行数据库迁移：`dotnet ef database update`
5. 启动Redis服务
6. 运行项目：`dotnet run --project src/EnterpriseMqttBroker.WebApi`

### 测试运行
- 单元测试：`dotnet test tests/EnterpriseMqttBroker.UnitTests/`
- 集成测试：`dotnet test tests/EnterpriseMqttBroker.IntegrationTests/`
- 负载测试：使用MQTT客户端工具或自定义负载测试脚本
